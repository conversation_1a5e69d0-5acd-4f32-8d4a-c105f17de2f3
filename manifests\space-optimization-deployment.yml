apiVersion: apps/v1
kind: Deployment
metadata:
  name: space-optimization-backend-deployment
  namespace: dll-space-optimization
spec:
  replicas: 1
  selector:
    matchLabels:
      app: space-optimization-backend
  template:
    metadata:
      labels:
        app: space-optimization-backend
    spec:
      imagePullSecrets:
        - name: k8sacrauth
      containers:
        - name: space-optimization-backend
          image: lmapaz1acrdllprd02.azurecr.io/space-optimization/optimization-backend-img
          ports:
            - containerPort: 8000
          env:
            - name: DB_HOST
              valueFrom:
                configMapKeyRef:
                  name: space-optimization-app-config
                  key: DB_HOST
            - name: DB_PORT
              valueFrom:
                configMapKeyRef:
                  name: space-optimization-app-config
                  key: DB_PORT
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: space-optimization-db-pwd
                  key: space-optimization-db-pwd-key
            - name: DB_USER
              valueFrom:
                configMapKeyRef:
                  name: space-optimization-app-config
                  key: DB_USER
            - name: DB_NAME
              valueFrom:
                configMapKeyRef:
                  name: space-optimization-app-config
                  key: DB_NAME
            - name: AZURE_AD_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: space-optimization-ad-clientid
                  key: space-optimization-ad-clientid-key
            - name: AZURE_AD_TENANT_ID
              valueFrom:
                secretKeyRef:
                  name: space-optimization-ad-tenantid
                  key: space-optimization-ad-tenantid-key
            - name: AZURE_AD_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: space-optimization-ad-clientsecret
                  key: space-optimization-ad-clientsecret-key
            - name: FASTAPI_BASE_URL
              valueFrom:
                configMapKeyRef:
                  name: space-optimization-app-config
                  key: FASTAPI_BASE_URL
          volumeMounts:
            - name: secrets-volume
              mountPath: "/mnt/secrets-store"
              readOnly: true
      volumes:
        - name: config-volume
          configMap:
            name: space-optimization-app-config
        - name: secrets-volume
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: "azure-space-optimization-secret-provider"
