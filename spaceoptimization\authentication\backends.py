import json
import base64
import requests
from rest_framework.authentication import BaseAuthentication
from rest_framework.exceptions import AuthenticationFailed
from django.conf import settings
from .models import Users


class AzureADAuthentication(BaseAuthentication):
    """
    Custom authentication backend for Azure AD tokens
    """
    
    def authenticate(self, request):
        """
        Authenticate the request and return a two-tuple of (user, token).
        """
        auth_header = request.META.get('HTTP_AUTHORIZATION')

        if not auth_header:
            return None

        try:
            # Extract token from "Bearer <token>" format
            auth_parts = auth_header.split()
            if len(auth_parts) != 2 or auth_parts[0].lower() != 'bearer':
                return None

            token = auth_parts[1]

            # Validate and decode the token
            user = self.validate_token(token)

            return (user, token)

        except Exception as e:
            raise AuthenticationFailed(f'Invalid token: {str(e)}')
    
    def validate_token(self, token):
        """
        Validate the Azure AD token and return the corresponding user
        """
        try:
            # Simple JWT decode without verification (basic implementation)
            # Split the JWT token into parts
            parts = token.split('.')
            if len(parts) != 3:
                raise AuthenticationFailed('Invalid token format')

            # Decode the payload (second part)
            payload = parts[1]
            # Add padding if needed
            payload += '=' * (4 - len(payload) % 4)
            decoded_bytes = base64.urlsafe_b64decode(payload)
            decoded_token = json.loads(decoded_bytes.decode('utf-8'))

            # Extract email from token
            email = decoded_token.get('email') or decoded_token.get('upn')

            if not email:
                raise AuthenticationFailed('Email not found in token')

            # Find user in database
            try:
                user = Users.objects.get(email=email, is_active=True)
                return user
            except Users.DoesNotExist:
                raise AuthenticationFailed('User not found or inactive')

        except json.JSONDecodeError:
            raise AuthenticationFailed('Invalid token format')
        except Exception as e:
            raise AuthenticationFailed(f'Token validation failed: {str(e)}')
    
    def authenticate_header(self, request):
        """
        Return a string to be used as the value of the `WWW-Authenticate`
        header in a `401 Unauthenticated` response.
        """
        return 'Bearer'
