import pandas as pd
import numpy as np
from decimal import Decimal
from typing import List, Dict, Any
import logging

logger = logging.getLogger(__name__)

class OutlierDetectionService:
    """
    Service class for detecting and imputing outliers in merchandise data
    """
    
    # Required columns for the outlier detection logic
    REQUIRED_COLUMNS = [
        'CLUSTER_NUM', 'LOC_CD', 'MONTH',
        'GRP_NM', 'DPT_NM', 'CLSS_NM', 'SUB_CLSS_NM',
        'TOTAL_LM', 'LM_CONTRIBUTION_IN_STORE'
    ]
    
    def __init__(self):
        self.hierarchy_levels = ['GRP_NM', 'DPT_NM', 'CLSS_NM', 'SUB_CLSS_NM']
    
    def validate_dataframe(self, df: pd.DataFrame, metric: str) -> tuple[bool, List[str]]:
        """
        Validate if DataFrame has all required columns
        
        Args:
            df: Input DataFrame
            metric: The metric column name (e.g., 'GMV')
            
        Returns:
            tuple: (is_valid, list_of_missing_columns)
        """
        required_cols = self.REQUIRED_COLUMNS.copy()
        required_cols.extend([f"{metric}_PER_DAY"])
        missing_cols = [col for col in required_cols if col not in df.columns]
        return len(missing_cols) == 0, missing_cols
    
    def preprocess_dataframe(self, df: pd.DataFrame, metric: str) -> pd.DataFrame:
        """
        Preprocess DataFrame - handle Decimal types and filter nulls
        Args:
            df: Input DataFrame
            metric: The metric column name (e.g., 'GMV', 'REVENUE')
        Returns:
            pd.DataFrame: Preprocessed DataFrame
        """
        df_processed = df.copy()
        # Convert Decimal columns to float
        for col in df_processed.columns:
            if df_processed[col].apply(type).eq(Decimal).any():
                df_processed[col] = df_processed[col].astype(float)
        # Apply initial filters
        if metric in df_processed.columns:
            df_processed = df_processed[df_processed[metric].notna()]
        df_processed = df_processed[df_processed['TOTAL_LM'].notna()]
        return df_processed
    
    def detect_outliers(self, hierarchy_levels: List[str], metric: str, df: pd.DataFrame) -> pd.DataFrame:
        """
        Detect outliers based on Z-scores for metric and LM contribution
        
        Args:
            hierarchy_levels: List of hierarchy column names
            metric: The metric to analyze (e.g., 'GMV')
            df: Input DataFrame
            
        Returns:
            pd.DataFrame: DataFrame with outlier detection columns
        """
        df_result = df.copy()
        group_cols = ["CLUSTER_NUM"] + hierarchy_levels
        
        # Calculate Z-scores for metric
        df_result[f"{metric}_PER_DAY_MEAN"] = df_result.groupby(group_cols)[f"{metric}_PER_DAY"].transform("mean")
        df_result[f"{metric}_PER_DAY_STD"] = df_result.groupby(group_cols)[f"{metric}_PER_DAY"].transform(lambda x: x.std(ddof=0))
        df_result[f"{metric}_PER_DAY_Z"] = (df_result[f"{metric}_PER_DAY"] - df_result[f"{metric}_PER_DAY_MEAN"]) / df_result[f"{metric}_PER_DAY_STD"]
        
        # Calculate Z-scores for LM contribution
        df_result["LM_CONT_MEAN"] = df_result.groupby(group_cols)["LM_CONTRIBUTION_IN_STORE"].transform("mean")
        df_result["LM_CONT_STD"] = df_result.groupby(group_cols)["LM_CONTRIBUTION_IN_STORE"].transform(lambda x: x.std(ddof=0))
        df_result["LM_CONT_Z"] = (df_result["LM_CONTRIBUTION_IN_STORE"] - df_result["LM_CONT_MEAN"]) / df_result["LM_CONT_STD"]
        
        # Define outliers
        df_result["LM_OUTLIER"] = df_result["LM_CONT_Z"].abs() > 2
        df_result[f"{metric}_OUTLIER"] = df_result[f"{metric}_PER_DAY_Z"].abs() < 1
        
        # Classify outlier status
        df_result[f"{metric}_OUTLIER_STATUS"] = np.select(
            [
                df_result["LM_OUTLIER"] & df_result[f"{metric}_OUTLIER"],
                df_result["LM_OUTLIER"] & ~df_result[f"{metric}_OUTLIER"],
            ],
            ["MAJOR_OUTLIER", "MINOR_OUTLIER"],
            default="NON_OUTLIER"
        )
        
        # Drop intermediate columns
        columns_to_drop = [
            f"{metric}_PER_DAY_MEAN", f"{metric}_PER_DAY_Z", f"{metric}_PER_DAY_STD",
            "LM_CONT_MEAN", "LM_CONT_STD", "LM_OUTLIER", f"{metric}_OUTLIER", "LM_CONT_Z"
        ]
        
        return df_result.drop(columns=columns_to_drop)
    
    def impute_outliers(self, hierarchy_levels: List[str], metric: str, df: pd.DataFrame) -> pd.DataFrame:
        """
        Impute outlier values using historical/future averages and cluster fallbacks
        
        Args:
            hierarchy_levels: List of hierarchy column names
            metric: The metric to analyze (e.g., 'GMV')
            df: Input DataFrame with outlier detection
            
        Returns:
            pd.DataFrame: DataFrame with imputed values
        """
        df_result = df.copy()
        group_cols = ["LOC_CD"] + hierarchy_levels
        
        # Create valid LM mask
        df_result["VALID_LM"] = np.where(
            (df_result[f"{metric}_OUTLIER_STATUS"] != "MAJOR_OUTLIER") & (df_result["TOTAL_LM"] > 0),
            df_result["TOTAL_LM"], np.nan
        )
        
        # Sort by grouping columns and month
        df_result = df_result.sort_values(group_cols + ["MONTH"]).reset_index(drop=True)
        
        def collect_lists(subdf):
            """Collect past and future LM values for each row"""
            vals = subdf["VALID_LM"].tolist()
            n = len(subdf)
            subdf = subdf.copy()
            subdf["PAST_3"] = [[x for x in vals[max(0, i-21):i] if pd.notna(x)][-3:] for i in range(n)]
            subdf["NEXT_3"] = [[x for x in vals[i+1:i+21] if pd.notna(x)][:3] for i in range(n)]
            subdf["avg_last_3"] = [np.mean(p) if len(p) == 3 else np.nan for p in subdf["PAST_3"]]
            subdf["avg_next_3"] = [np.mean(n) if len(n) == 3 else np.nan for n in subdf["NEXT_3"]]
            return subdf
        
        df_result = df_result.groupby(group_cols, group_keys=False).apply(collect_lists)
        
        # Create fallback values at cluster level
        valid_mask = (df_result[f"{metric}_OUTLIER_STATUS"] != "MAJOR_OUTLIER") & (df_result["TOTAL_LM"] > 0)
        ref_df = df_result[valid_mask].copy()
        
        fallback_df = ref_df.groupby(
            ["CLUSTER_NUM", "MONTH"] + hierarchy_levels
        )["TOTAL_LM"].mean().reset_index().rename(columns={"TOTAL_LM": "FALLBACK_CLUSTER_LM"})
        
        df_result = df_result.merge(fallback_df, on=["CLUSTER_NUM", "MONTH"] + hierarchy_levels, how="left")
        
        # First level imputation using historical averages
        first_imputed = np.where(
            df_result[f"{metric}_OUTLIER_STATUS"] == "MAJOR_OUTLIER",
            df_result["avg_last_3"].combine_first(df_result["avg_next_3"]).fillna(0).round(2),
            df_result["TOTAL_LM"]
        )
        
        df_result["FIRST_LEVEL_IMPUTED_LM"] = first_imputed
        
        # Final imputation with cluster fallback
        df_result[f"{metric}_SUGGESTED_TOTAL_LM"] = np.where(
            (df_result[f"{metric}_OUTLIER_STATUS"] == "MAJOR_OUTLIER") & (df_result["FIRST_LEVEL_IMPUTED_LM"] == 0),
            df_result["FALLBACK_CLUSTER_LM"].fillna(0).round(2),
            df_result["FIRST_LEVEL_IMPUTED_LM"]
        )
        
        # Clean up intermediate columns
        columns_to_drop = [
            "VALID_LM", "avg_last_3", "avg_next_3", "FALLBACK_CLUSTER_LM", "NEXT_3", "PAST_3", "FIRST_LEVEL_IMPUTED_LM"
        ]
        
        return df_result.drop(columns=columns_to_drop)
    
    def process_outliers(self, df: pd.DataFrame, metric: str, 
                        hierarchy_levels: List[str] = None) -> Dict[str, Any]:
        """
        Main method to process outlier detection and imputation
        Args:
            df: Input DataFrame
            metric: The metric column to analyze (default: 'GMV')
            hierarchy_levels: Hierarchy levels (default: uses class attribute)
        Returns:
            dict: Contains 'success', 'data', 'message', and 'stats'
        """
        try:
            if hierarchy_levels is None:
                hierarchy_levels = self.hierarchy_levels
            # Validate input DataFrame
            is_valid, missing_cols = self.validate_dataframe(df, metric)
            if not is_valid:
                return {
                    'success': False,
                    'data': None,
                    'message': f"Missing required columns: {missing_cols}",
                    'stats': {}
                }
            # Preprocess data
            df_processed = self.preprocess_dataframe(df, metric)
            logger.info(f"Preprocessed data shape: {df_processed.shape}")
            # Detect outliers
            df_with_outliers = self.detect_outliers(hierarchy_levels, metric, df_processed)
            logger.info(f"Outlier detection completed")
            # Impute outliers
            final_df = self.impute_outliers(hierarchy_levels, metric, df_with_outliers)
            logger.info(f"Outlier imputation completed")
            # Calculate statistics
            stats = self._calculate_stats(final_df, metric)
            return {
                'success': True,
                'data': final_df,
                'message': 'Outlier processing completed successfully',
                'stats': stats
            }
        except Exception as e:
            logger.error(f"Error in outlier processing: {str(e)}")
            return {
                'success': False,
                'data': None,
                'message': f"Processing failed: {str(e)}",
                'stats': {}
            }
    
    def _calculate_stats(self, df: pd.DataFrame, metric: str) -> Dict[str, Any]:
        """Calculate processing statistics"""
        total_rows = len(df)
        major_outliers = len(df[df[f"{metric}_OUTLIER_STATUS"] == "MAJOR_OUTLIER"])
        minor_outliers = len(df[df[f"{metric}_OUTLIER_STATUS"] == "MINOR_OUTLIER"])
        non_outliers = len(df[df[f"{metric}_OUTLIER_STATUS"] == "NON_OUTLIER"])
        
        return {
            'total_rows': total_rows,
            'major_outliers': major_outliers,
            'minor_outliers': minor_outliers,
            'non_outliers': non_outliers,
            'major_outlier_percentage': round((major_outliers / total_rows) * 100, 2) if total_rows > 0 else 0,
            'minor_outlier_percentage': round((minor_outliers / total_rows) * 100, 2) if total_rows > 0 else 0
        }
