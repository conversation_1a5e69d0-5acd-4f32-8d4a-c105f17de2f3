from rest_framework import serializers
from .models import ScenarioMetad
import json


class ScenarioMetadSerializer(serializers.ModelSerializer):
    class Meta:
        model = ScenarioMetad
        fields = '__all__'


class ClusterRequestSerializer(serializers.Serializer):
    concept = serializers.CharField()
    territory = serializers.CharField()
    # loc_codes = serializers.ListField(
    #     child=serializers.CharField(), allow_empty=False
    # )
    
class PreoptRequestSerializer(serializers.Serializer):
    concept = serializers.CharField()
    territory = serializers.CharField()
    scenario_id = serializers.IntegerField()
    loc_codes = serializers.ListField()
    performance_metric = serializers.CharField()


class StoreSelectionDropdownSerializer(serializers.Serializer):
    concept = serializers.CharField()
    territory = serializers.CharField()

class ClusterDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = None  # will be set dynamically
        fields = [
            "loc_cd", "loc_nm", "rgn_nm", "revenue", "units", "customers", "total_invoice", "area_sqft",
            "cluster_num","new_cluster_num", "volume_contribution", "ethnicity_contribution",
            "revenue_per_sqft"
        ]
class PreoptDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = None  # will be set dynamically
        fields = fields = [
    "territory_nm",
    "loc_cd",
    "loc_nm",
    "stnd_trrtry_nm",
    "rgn_nm",
    "grp_nm",
    "dpt_nm",
    "clss_nm",
    "sub_clss_nm",
    "month",
    "mnth_avg_soh",
    "mnth_avg_itm_cnt",
    "mnth_avg_optn_cnt",
    "mnth_end_soh",
    "mnth_end_itm_cnt",
    "mnth_end_optn_cnt",
    "net_sls_amt",
    "rtl_qty",
    "gmv",
    "inv_cnt",
    "cust_cnt",
    "str_visits",
    "str_cust_cnt",
    "cust_pen",
    "spc",
    "margin_perc",
    "asp",
    "sls_per_inv",
    "units_per_inv",
    "ros",
    "cover",
    "total_lm",
    "gmv_per_day",
    "gmv_per_lm",
    "lm_contribution_in_store",
    "outlier_status",
    "suggested_total_lm",
    "last_update_dt_tm",
    "scenario_id"
]


class ClusterUpdateSerializer(serializers.Serializer):
    loc_cd = serializers.IntegerField()
    cluster_num = serializers.IntegerField()
    scenario_id = serializers.IntegerField()
    concept = serializers.CharField(required=False)
    
class PreoptUpdateSerializer(serializers.Serializer):
    loc_cd = serializers.IntegerField()
    cluster_num = serializers.IntegerField()
    concept = serializers.CharField(required=False)
    scenario_id = serializers.IntegerField()
    territory = serializers.CharField()

class StoreSelectionDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = None
        fields = ['loc_cd', 'loc_nm']


class OutlierUpdateSerializer(serializers.Serializer):
    sub_clss_nm = serializers.CharField(max_length=100, required=True)
    loc_cd = serializers.CharField(max_length=20, required=True)
    outlier_status = serializers.CharField(max_length=20, required=True)
    outlier_status_final = serializers.CharField(max_length=20, required=True)
    month = serializers.IntegerField(required=True)
    scenario_id = serializers.IntegerField(required=True)

class testAndControlStoreSerializer(serializers.Serializer):
    concept = serializers.CharField()
    territory = serializers.CharField()
    
class OutlierDetectionRequestSerializer(serializers.Serializer):
    scenario_id = serializers.IntegerField(required=True)
    concept = serializers.CharField(max_length=50, required=True)
    territory = serializers.CharField(max_length=50, required=True)
    performance_metric = serializers.CharField(required=True)

class OutlierDetectionResponseSerializer(serializers.Serializer):
    message = serializers.CharField()
    processed_stores = serializers.IntegerField()
    outliers_found = serializers.IntegerField()
    major_outliers = serializers.IntegerField()
    minor_outliers = serializers.IntegerField()
    execution_time = serializers.FloatField()
    
class InsertTestControlStrRequestSerializer(serializers.Serializer):
    scenario_id = serializers.IntegerField(required=True)
    store_codes = serializers.CharField(max_length=256, required=True)

class InsertTestControlStrResponseSerializer(serializers.Serializer):
    message = serializers.CharField()
    scenario_id = serializers.IntegerField()
    updated_stores = serializers.IntegerField()
    execution_time = serializers.FloatField()

class GraphDataRequestSerializer(serializers.Serializer):
    concept = serializers.CharField(max_length=64, required=True)
    group = serializers.ListField(child=serializers.CharField(), required=False)
    department = serializers.ListField(child=serializers.CharField(), required=False)
    class_field = serializers.ListField(child=serializers.CharField(), source='class', required=False)
    sub_class = serializers.ListField(child=serializers.CharField(), required=False)
    from_month = serializers.CharField(required=True)  # Format: YYYY-MM
    to_month = serializers.CharField(required=True)    # Format: YYYY-MM
    territory_name = serializers.CharField(max_length=64, required=True)
    metric = serializers.CharField(max_length=64, required=True)
    loc_cd = serializers.ListField(child=serializers.CharField(), required=False)
    

class GraphDataPointSerializer(serializers.Serializer):
    month = serializers.CharField()
    total_lm = serializers.FloatField()
    productivity = serializers.FloatField()
    gmv_per_lm = serializers.FloatField()
    group_name = serializers.CharField(required=False)
    department_name = serializers.CharField(required=False)
    class_name = serializers.CharField(required=False)
    sub_class_name = serializers.CharField(required=False)
    metric_value = serializers.FloatField()
    metric = serializers.CharField(required=False)


class GraphDataResponseSerializer(serializers.Serializer):
    message = serializers.CharField()
    data_points = GraphDataPointSerializer(many=True)
    total_records = serializers.IntegerField()
    aggregation_level = serializers.CharField()
    filters_applied = serializers.DictField()
    
class MetricGraphRequestSerializer(serializers.Serializer):
    concept = serializers.CharField(max_length=64, required=True)
    scenario_id = serializers.IntegerField(required=True)
    # loc_cd = serializers.CharField(max_length=20, required=True)
    metric = serializers.CharField(max_length=50, required=True)


class SubclassDataSerializer(serializers.Serializer):
    sub_class_name = serializers.CharField()
    metric_value = serializers.FloatField()
    rank = serializers.IntegerField()
    category = serializers.CharField()  # 'top' or 'bottom'

class MetricGraphResponseSerializer(serializers.Serializer):
    message = serializers.CharField()
    store_code = serializers.CharField()
    store_name = serializers.CharField()
    latest_month = serializers.CharField()
    metric = serializers.CharField()
    metric_display_name = serializers.CharField()
    top_5 = SubclassDataSerializer(many=True)
    bottom_5 = SubclassDataSerializer(many=True)
    total_subclasses = serializers.IntegerField()
    
class GDCSDataRequestSerializer(serializers.Serializer):
    """Serializer for request validation"""
    concept = serializers.CharField(max_length=100, required=True)
    territory_name = serializers.CharField()
