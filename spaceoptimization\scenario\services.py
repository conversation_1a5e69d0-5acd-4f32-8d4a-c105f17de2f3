from typing import List, Dict, Any, <PERSON>ple
from datetime import datetime
from dateutil.relativedelta import relativedelta
from django.db.models import Sum, Avg, QuerySet, Q, FloatField, Max, OuterRef, Subquery
from django.db.models.functions import Coalesce
from .models import SqftFileData
from collections import defaultdict
from decimal import Decimal, ROUND_HALF_UP


class DataAggregationService:
    """Service for data aggregation operations"""
    
    def __init__(self):
        self.grouping_fields = ['loc_cd','loc_nm', 'grp_nm', 'dpt_nm', 'clss_nm', 'sub_clss_nm']

    def get_latest_available_data(self, data: List[Dict[str, Any]]) -> Dict[Tuple, Dict[str, float]]:
        """Get mnth_end_soh, ros, cover for the latest available month per GDCS combination"""
        try:
            # Group by GDCS and find latest month for each
            latest_month  = max(int(row.get('month', '0')) for row in data)
            gdcs_latest = {}
            for row in data:
                if int(row.get("month", 0)) != latest_month:
                    continue 
                gdcs_key = tuple(row.get(field, '') for field in self.grouping_fields)
                
                gdcs_latest[gdcs_key] = {
                    'month': latest_month,
                    'mnth_end_soh': round(float(row.get('mnth_end_soh', 0) or 0), 2),
                    'ros': round(float(row.get('ros', 0.0) or 0.0), 2),
                    'cover': round(float(row.get('cover', 0.0) or 0.0), 2),
                    'area_in_sqft': round(float(row.get('area_in_sqft', 0) or 0), 2),
                    'gmroi': round(float(row.get('gmroi', 0.0) or 0.0), 2)
                }
            
            # Remove month from the result
            return {k: {key: val for key, val in v.items() if key != 'month'} 
                    for k, v in gdcs_latest.items()}
            
        except Exception as e:
            print(f"Error getting latest available data: {e}")
            return {}

    def aggregate_data(self, data: List[Dict[str, Any]],data_filtered: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]],  Dict[Tuple, Dict[str, float]]]:
        """Aggregate main metrics by GDCS combination (works with list of dicts)"""
        try:
            # Initialize aggregation dictionary
            aggregated_dict = defaultdict(lambda: {
                'total_lm_sum': 0.0,
                'total_optn_cnt_sum': 0.0,
                'total_soh_sum': 0.0,
                'total_net_sls_sum': 0.0,
                'total_gmv_sum': 0.0,
                'lm_contrib_values': [],  # Store all values for averaging
                'total_area_sqft': 0.0,
                'gmroi': [],
            })
            # Aggregate data by GDCS key
            for row in data:
                gdcs_key = tuple(row.get(field, '') for field in self.grouping_fields)
                agg = aggregated_dict[gdcs_key]

                # Sum fields across all months (matching the QuerySet logic)
                agg['total_lm_sum'] += float(row.get('total_lm', 0.0) or 0.0)
                agg['total_optn_cnt_sum'] += float(row.get('mnth_avg_optn_cnt', 0.0) or 0.0)
                agg['total_soh_sum'] += float(row.get('mnth_avg_soh', 0.0) or 0.0)
                agg['total_net_sls_sum'] += float(row.get('net_sls_amt', 0.0) or 0.0)
                agg['total_gmv_sum'] += float(row.get('gmv', 0.0) or 0.0)
                
                # Collect lm_contribution values for averaging
                lm_contrib = row.get('lm_contribution_in_store')
                if lm_contrib is not None:
                    agg['lm_contrib_values'].append(lm_contrib)
                
                gmroi_values = row.get('gmroi')
                if gmroi_values is not None:
                    agg['gmroi'].append(gmroi_values)
            # Convert to list format matching original QuerySet output
            aggregated = []
            for gdcs_key, values in aggregated_dict.items():
                result = dict(zip(self.grouping_fields, gdcs_key))
                
                # Add aggregated values with exact same field names as QuerySet version
                result.update({
                    'total_lm_sum': round(values['total_lm_sum'], 2),
                    'total_optn_cnt_sum': round(values['total_optn_cnt_sum'], 2),
                    'total_soh_sum': round(values['total_soh_sum'], 2),
                    'total_net_sls_sum': round(values['total_net_sls_sum'], 2),
                    'total_gmv_sum': round(values['total_gmv_sum'], 2),
                    'avg_lm_contribution': round(
                        sum(values['lm_contrib_values']) / len(values['lm_contrib_values']), 2
                    ) if values['lm_contrib_values'] else 0.0,
                    # 'total_area_sqft': round(values['total_area_sqft'], 2),
                    'gmroi': round(sum(values['gmroi']) / len(values['gmroi']), 2) if values['gmroi'] else 0.0,
                })
                aggregated.append(result)

            # Get latest available data for mnth_end_soh, ros, cover
            latest_month_data = self.get_latest_available_data(data_filtered)

            return aggregated, latest_month_data

        except Exception as e:
            print(f"Error in data aggregation: {e}")
            return [], {}

    def calculate_store_totals(self, aggregated_data: List[Dict[str, Any]], 
                            latest_month_data: Dict[Tuple, Dict[str, float]],
                            ) -> Dict[str, Dict[str, float]]:
        """Calculate store-level totals for all metrics"""
        try:
            store_totals = {}
            # Group data by store
            store_data = {}
            for item in aggregated_data:
                loc_cd = item.get('loc_cd', '')
                if loc_cd not in store_data:
                    store_data[loc_cd] = {
                        'total_lm_sum': 0.0,
                        'total_optn_cnt_sum': 0.0,
                        'total_soh_sum': 0.0,
                        'total_net_sls_sum': 0.0,
                        'total_gmv_sum': 0.0,
                        'total_inv_cnt_sum': 0.0,
                        'total_mnth_end_soh_sum': 0.0,
                        'total_ros_sum': 0.0,
                        'total_cover_sum': 0.0,
                        'total_area_sqft_sum': 0.0
                    }
                
                # Sum regular metrics (across all months)
                for field in ['total_lm_sum', 'total_optn_cnt_sum', 'total_soh_sum', 
                            'total_net_sls_sum', 'total_gmv_sum', 'total_area_sqft_sum']:
                    store_data[loc_cd][field] += float(item.get(field, 0) or 0)
                
                # Add latest month data for mnth_end_soh, ros, cover
                gdcs_key = (
                    item.get('loc_cd', ''),
                    item.get('loc_nm',''),
                    item.get('grp_nm', ''),
                    item.get('dpt_nm', ''),
                    item.get('clss_nm', ''),
                    item.get('sub_clss_nm', '')
                )
                
                if gdcs_key in latest_month_data:
                    latest_data = latest_month_data[gdcs_key]
                    store_data[loc_cd]['total_inv_cnt_sum'] += float(latest_data.get('mnth_end_soh', 0))
                    store_data[loc_cd]['total_mnth_end_soh_sum'] += float(latest_data.get('mnth_end_soh', 0))  # Add this line
                    store_data[loc_cd]['total_ros_sum'] += float(latest_data.get('ros', 0))
                    store_data[loc_cd]['total_cover_sum'] += float(latest_data.get('cover', 0))
                    store_data[loc_cd]['total_area_sqft_sum'] += float(latest_data.get('area_in_sqft', 0))
            
            # Add sqft totals by store
            # for (loc_cd, grp_nm, dpt_nm, clss_nm, sub_clss_nm), sqft in sqft_data.items():
            #     if loc_cd in store_data:
            #         store_data[loc_cd]['total_sqft_sum'] += float(sqft)
            
            # Round all values to 2 decimal places
            for loc_cd, totals in store_data.items():
                store_totals[loc_cd] = {
                    key: round(value, 2) for key, value in totals.items()
                }
            
            return store_totals
            
        except Exception as e:
            print(f"Error calculating store totals: {e}")
            return {}
class FilterService:
    """Service for handling data filtering operations"""
    
    def __init__(self):
        self.supported_filters = {
            'store_id[]': 'loc_cd',
            'group[]': 'grp_nm',
            'department[]': 'dpt_nm',
            'class[]': 'clss_nm',
            'sub_class[]': 'sub_clss_nm'
        }

    def get_supported_filters(self) -> dict:
        return self.supported_filters

    def apply_filters(self, queryset, filters: dict):
        """Apply filters to either queryset or list of dicts"""
        if isinstance(queryset, list):
            return self._apply_filters_to_list(queryset, filters)
        else:
            return self._apply_filters_to_queryset(queryset, filters)

    def _apply_filters_to_queryset(self, queryset, filters: dict):
        """Apply filters to a Django queryset"""
        filter_kwargs = {}

        for param_key, param_values in filters.items():
            if param_key in self.supported_filters and param_values:
                if not isinstance(param_values, (list, tuple)):
                    param_values = [param_values]

                param_values = [v for v in param_values if v not in [None, '']]
                if not param_values:
                    continue

                db_lookup = self.supported_filters[param_key] + "__in"
                filter_kwargs[db_lookup] = param_values

        if filter_kwargs:
            return queryset.filter(**filter_kwargs)
        
        return queryset

    def _apply_filters_to_list(self, data: list, filters: dict):
        """Apply filters to list of dictionaries (raw SQL results)"""
        if not filters:
            return data

        def matches(row):
            for param_key, param_values in filters.items():
                if param_key not in self.supported_filters:
                    continue
                if not isinstance(param_values, (list, tuple)):
                    param_values = [param_values]
                param_values = [v for v in param_values if v not in [None, '']]
                if not param_values:
                    continue
                field = self.supported_filters[param_key]
                if row.get(field) not in param_values:
                    return False
            return True

        return [row for row in data if matches(row)]

class SqftService:
    """Service for SQFT data operations"""
    
    def get_sqft_data(self, gdcs_keys: List[Tuple[str, str, str, str, str]], scenario_id: int) -> Dict[Tuple, float]:
        """Retrieve sqft data for given GDCS combinations"""
        try:
            if not gdcs_keys:
                return {}

            loc_cds = {str(k[0]) for k in gdcs_keys}
            grp_nms = {str(k[2]) for k in gdcs_keys}
            dpt_nms = {str(k[3]) for k in gdcs_keys}
            clss_nms = {str(k[4]) for k in gdcs_keys}
            sub_clss_nms = {str(k[5]) for k in gdcs_keys}


            sqft_qs = (
                SqftFileData.objects
                .filter(
                    Q(loc_cd__in=loc_cds) &
                    Q(grp_nm__in=grp_nms) &
                    Q(dpt_nm__in=dpt_nms) &
                    Q(clss_nm__in=clss_nms) &
                    Q(sub_clss_nm__in=sub_clss_nms) &
                    Q(scenario_id=scenario_id)
                )
                .values('loc_cd', 'grp_nm', 'dpt_nm', 'clss_nm', 'sub_clss_nm','sqft')
            )

            print(sqft_qs.query)
            sqft_dict = {
                (row['loc_cd'], row['grp_nm'], row['dpt_nm'], row['clss_nm'], row['sub_clss_nm']): row['sqft'] or 0
                for row in sqft_qs
            }
            
            return sqft_dict
            
        except Exception as e:
            print(f"Error retrieving sqft data: {e}")
            return {}


class MetricsCalculationService:
    """Service for calculating derived metrics"""
    
    def safe_divide(self, numerator: float, denominator: float, fallback: float = 0.0) -> float:
        """Safe division with rounding to 2 decimal places"""
        return round(numerator / denominator if denominator > 0 else fallback, 4)
    
    def calculate_metrics(self, item: Dict[str, Any], latest_month_data: Dict[Tuple, Dict[str, float]],
                         months_in_period: int, days_in_period: int, 
                         store_totals: Dict[str, Dict[str, float]]) -> Dict[str, float]:
        """Calculate all derived metrics"""
        try:
            # Get GDCS key and store key
            gdcs_key = (
                item.get('loc_cd', ''),
                item.get('loc_nm',''),
                item.get('grp_nm', ''),
                item.get('dpt_nm', ''),
                item.get('clss_nm', ''),
                item.get('sub_clss_nm', '')
               )
            gdcs_key_sqft = (
                item.get('loc_cd', ''),
                item.get('grp_nm', ''),
                item.get('dpt_nm', ''),
                item.get('clss_nm', ''),
                item.get('sub_clss_nm', '')
               )
            store_key = item.get('loc_cd', '')
            
            # Get sqft and store totals
            # sqft = sqft_data.get(gdcs_key_sqft, 0.0)
            # sqft = item.get('total_area_sqft_sum', 0.0)
            latest_data = latest_month_data.get(gdcs_key, {})
            sqft = latest_data.get('area_in_sqft', 0.0)
            store_total = store_totals.get(store_key, {})
            total_area_sqft_sum = store_total.get('total_area_sqft_sum', 0.0)

            # Basic metrics (averaged over months) - EXCEPT mnth_end_soh, ros, cover
            lm = self.safe_divide(item.get('total_lm_sum', 0), months_in_period)
            option_count = self.safe_divide(item.get('total_optn_cnt_sum', 0), months_in_period)
            soh = self.safe_divide(item.get('total_soh_sum', 0), months_in_period)
            rev = round(float(item.get('total_net_sls_sum', 0) or 0), 0)
            gmv = round(float(item.get('total_gmv_sum', 0) or 0), 0)
            mnth_end_soh = round(float(latest_data.get('mnth_end_soh', 0) or 0), 2)
            ros = round(float(latest_data.get('ros', 0) or 0), 2)
            cover = round(float(latest_data.get('cover', 0) or 0), 2)
            rev_per_day = self.safe_divide(item.get('total_net_sls_sum', 0), days_in_period)
            gmv_per_day = self.safe_divide(item.get('total_gmv_sum', 0), days_in_period)
            rev_per_lm = self.safe_divide(rev, lm)
            gmv_per_lm = self.safe_divide(gmv, lm)
            rev_per_sqft = self.safe_divide(rev, sqft)
            gmv_per_sqft = self.safe_divide(gmv, sqft)

            # Density metrics
            option_density = self.safe_divide(option_count, lm)
            stock_density = self.safe_divide(soh, lm)

            # Per day per linear meter metrics
            gmv_per_lm_per_day = self.safe_divide(gmv_per_day, lm)
            rev_per_lm_per_day = self.safe_divide(rev_per_day, lm)

            # SQFT-based metrics
            lm_contribution_in_store = round(float(item.get('avg_lm_contribution', 0) or 0), 2)
            gmroi = round(float(item.get('gmroi', 0) or 0), 2)

            sqft_contribution_in_store = self.safe_divide(sqft, total_area_sqft_sum) 
            diff_lm_sqft_contrib = round(abs(sqft_contribution_in_store - lm_contribution_in_store), 2)
            sqft_contribution_in_store = sqft_contribution_in_store*100
            fixture_density = self.safe_divide(lm, sqft)
            rev_per_sqft_per_day = self.safe_divide(rev_per_day, sqft)
            gmv_per_sqft_per_day = self.safe_divide(gmv_per_day, sqft)

            return {
                'sqft': round(sqft, 2),
                'lm': lm,
                'option_count': option_count,
                'soh': soh,
                'mnth_end_soh': mnth_end_soh,
                'rev': rev,
                'gmv': gmv,
                'ros': ros,
                'cover': cover,
                'gmroi': gmroi,
                'rev_per_day': rev_per_day,
                'gmv_per_day': gmv_per_day,
                'option_density': option_density,
                'stock_density': stock_density,
                'gmv_per_lm_per_day': gmv_per_lm_per_day,
                'rev_per_lm_per_day': rev_per_lm_per_day,
                'fixture_density': fixture_density,
                'sqft_contribution_in_store': sqft_contribution_in_store,
                'diff_lm_sqft_contrib': diff_lm_sqft_contrib,
                'lm_contribution_in_store': lm_contribution_in_store,
                'rev_per_sqft_per_day': rev_per_sqft_per_day,
                'gmv_per_sqft_per_day': gmv_per_sqft_per_day,
                'rev_per_lm': rev_per_lm,
                'gmv_per_lm': gmv_per_lm,
                'rev_per_sqft': rev_per_sqft,
                'gmv_per_sqft': gmv_per_sqft
                
            }
            
        except Exception as e:
            print(f"Error calculating metrics: {e}")
            return {}


class RankingService:
    """Service for calculating rankings"""
    
    def __init__(self):
        self.ranking_fields = [
            'lm', 'sqft', 'rev_per_sqft_per_day', 'gmv_per_sqft_per_day',
            'rev_per_lm_per_day', 'gmv_per_lm_per_day', 'fixture_density',
            'sqft_contribution_in_store', 'diff_lm_sqft_contrib', 'lm_contribution_in_store'
        ]
    
    def calculate_ranks(self, processed_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Calculate store-level rankings for all items"""
        try:
            # Group data by store for ranking
            store_groups = {}
            for item in processed_data:
                loc_cd = item.get('loc_cd', '')
                if loc_cd not in store_groups:
                    store_groups[loc_cd] = []
                store_groups[loc_cd].append(item)
            
            # Calculate ranks within each store
            for loc_cd, store_items in store_groups.items():
                for field in self.ranking_fields:
                    # Sort items by field value (descending)
                    sorted_items = sorted(
                        store_items, 
                        key=lambda x: x.get(field, 0), 
                        reverse=True
                    )
                    
                    # Assign ranks
                    for rank, item in enumerate(sorted_items, 1):
                        item[f'{field}_rank'] = rank
            
            return processed_data
            
        except Exception as e:
            print(f"Error calculating ranks: {e}")
            return processed_data


class PeriodCalculationService:
    """Service for period-related calculations"""
    
    def calculate_period_metrics(self, ref_period: Dict[str, str]) -> Dict[str, int]:
        """Calculate months and days in period from ref_period"""
        try:
            start_month = datetime.strptime(ref_period['start'], '%Y%m')
            end_month = datetime.strptime(ref_period['end'], '%Y%m')
            
            months_in_period = (relativedelta(end_month, start_month).months +
                               (relativedelta(end_month, start_month).years * 12) + 1)
            days_in_period = months_in_period * 30  # approximate month length
            
            return {
                'months_in_period': months_in_period,
                'days_in_period': days_in_period
            }
        except Exception as e:
            print(f"Error calculating period metrics: {e}")
            return {'months_in_period': 1, 'days_in_period': 30}


class HealthMetricsProcessing:
    """Main service that coordinates all other services"""
    
    def __init__(self):
        self.filter_service = FilterService()
        self.aggregation_service = DataAggregationService()
        self.sqft_service = SqftService()
        self.metrics_service = MetricsCalculationService()
        self.ranking_service = RankingService()
        self.period_service = PeriodCalculationService()
    
    def process_data(self, base_queryset: list,base_queryset_all: list, ref_period: Dict[str, str],
                     filter_params: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Complete data processing workflow"""
        try:
            # Calculate period metrics
            period_metrics = self.period_service.calculate_period_metrics(ref_period)
            months_in_period = period_metrics['months_in_period']
            days_in_period = period_metrics['days_in_period']
            
            # Apply additional filters
            if filter_params:
                base_queryset = self.filter_service.apply_filters(base_queryset, filter_params)
            
            # Aggregate data and get latest month data
            aggregated_data, latest_month_data = self.aggregation_service.aggregate_data(base_queryset,base_queryset_all)
            if not aggregated_data:
                return []
            
            # Get GDCS keys for sqft lookup
            gdcs_keys = [
                (item['loc_cd'], item['loc_nm'], item['grp_nm'], item['dpt_nm'], item['clss_nm'], item['sub_clss_nm'])
                for item in aggregated_data
            ]
            
            # Get sqft data
            # sqft_data = self.sqft_service.get_sqft_data(gdcs_keys,scenario_id)
            
            # Calculate store totals (including latest month data)
            store_totals = self.aggregation_service.calculate_store_totals(
                aggregated_data, latest_month_data
            )
            
            # Calculate metrics for each item
            processed_data = []
            for item in aggregated_data:
                metrics = self.metrics_service.calculate_metrics(
                    item, latest_month_data, months_in_period, days_in_period, 
                    store_totals
                )
                
                # Combine original item with calculated metrics
                combined_item = {**item, **metrics}
                processed_data.append(combined_item)
            
            # Calculate rankings
            processed_data = self.ranking_service.calculate_ranks(processed_data)
            return processed_data
            
        except Exception as e:
            print(f"Error in data health metrics processing: {e}")
            return []
        

class SummaryTotalCalculator:
    """Helper class to calculate GDC totals and Summary totals from processed data"""
    
    # Fields that should be summed (excluding latest month fields)
    SUM_FIELDS = [
        'sqft', 'lm', 'option_count', 'soh', 'rev', 'gmv',
        'rev_per_day', 'gmv_per_day', 'fixture_density', 'sqft_contribution_in_store',
        'diff_lm_sqft_contrib', 'lm_contribution_in_store', 'rev_per_sqft_per_day',
        'gmv_per_sqft_per_day'
    ]
    
    # Latest month fields that should be summed at aggregation level
    LATEST_MONTH_SUM_FIELDS = ['mnth_end_soh', 'ros', 'cover']
    
    # Fields that should be averaged
    RANK_FIELDS = [
        'lm_rank', 'sqft_rank', 'rev_per_sqft_per_day_rank', 'gmv_per_sqft_per_day_rank',
        'rev_per_lm_per_day_rank', 'gmv_per_lm_per_day_rank'
    ]
    
    # Fields that need to be recalculated from base metrics
    CALCULATED_FIELDS = ['option_density', 'stock_density', 'gmv_per_lm_per_day', 'rev_per_lm_per_day']
        
    def safe_divide(self, numerator, denominator, decimal_places=2):
        """Safely divide two numbers, returning 0 if denominator is 0"""
        if denominator == 0:
            return 0.0
        result = numerator / denominator
        return float(Decimal(str(result)).quantize(Decimal('0.' + '0' * decimal_places), 
                                                 rounding=ROUND_HALF_UP))
    
    def calculate_derived_metrics(self, aggregated_data):
        """Calculate derived metrics from base summed values"""
        # Option density = option_count / lm (if lm > 0)
        aggregated_data['option_density'] = self.safe_divide(
            aggregated_data['option_count'], aggregated_data['lm']
        )
        
        # Stock density = soh / lm (if lm > 0)  
        aggregated_data['stock_density'] = self.safe_divide(
            aggregated_data['soh'], aggregated_data['lm']
        )
        
        # GMV per LM per day = gmv_per_day / lm (if lm > 0)
        aggregated_data['gmv_per_lm_per_day'] = self.safe_divide(
            aggregated_data['gmv_per_day'], aggregated_data['lm']
        )
        
        # Revenue per LM per day = rev_per_day / lm (if lm > 0)
        aggregated_data['rev_per_lm_per_day'] = self.safe_divide(
            aggregated_data['rev_per_day'], aggregated_data['lm']
        )
        
        return aggregated_data
    
    def aggregate_group_data(self, data_list, group_fields):
        """Aggregate data by specified group fields"""
        groups = defaultdict(list)
        
        # Group data by specified fields
        for item in data_list:
            group_key = tuple(item.get(field, '') for field in group_fields)
            groups[group_key].append(item)
        
        aggregated_results = []
        
        for group_key, group_items in groups.items():
            agg_record = {}
            
            for i, field in enumerate(group_fields):
                agg_record[field] = group_key[i]
            
            agg_record['grp_nm'] = agg_record.get('grp_nm', None)
            agg_record['dpt_nm'] = agg_record.get('dpt_nm', None)
            agg_record['clss_nm'] = agg_record.get('clss_nm', None)
            
            # Sum regular fields
            for field in self.SUM_FIELDS:
                field_sum = sum(item.get(field, 0) for item in group_items)
                agg_record[field] = round(field_sum, 2)
            
            # Sum latest month fields (these are already latest month values at GDCS level)
            for field in self.LATEST_MONTH_SUM_FIELDS:
                field_sum = sum(item.get(field, 0) for item in group_items)
                agg_record[field] = round(field_sum, 2)
            
            # Calculate averages for rank fields
            for field in self.RANK_FIELDS:
                ranks = [item.get(field, 0) for item in group_items if item.get(field, 0) > 0]
                if ranks:
                    avg_rank = sum(ranks) / len(ranks)
                    agg_record[field] = round(avg_rank)  # Round to integer
                else:
                    agg_record[field] = 0.0
            
            # Calculate derived metrics 
            agg_record = self.calculate_derived_metrics(agg_record)
            
            aggregated_results.append(agg_record)
        
        return aggregated_results
    
    def calculate_summary_totals(self, processed_data=None):
        """Calculate summary totals array with hierarchical structure"""
        data = processed_data or self.processed_data
        
        if not data:
            return []
        
        summary_totals = []
        
        hierarchies = [
            (['loc_cd', 'grp_nm', 'dpt_nm', 'clss_nm'], 'Class'),
            (['loc_cd','grp_nm', 'dpt_nm'], 'Department'),
            (['loc_cd', 'grp_nm'], 'Group')
        ]
        
        for group_fields, level_name in hierarchies:
            aggregated = self.aggregate_group_data(data, group_fields)
            
            for record in aggregated:
                record['summary_level'] = level_name
                
                original_grp_nm = record.get('grp_nm', None)
                original_dpt_nm = record.get('dpt_nm', None)
                original_clss_nm = record.get('clss_nm', None)

                if level_name == 'Store':
                    record['grp_nm'] = None
                    record['dpt_nm'] = None
                    record['clss_nm'] = None
                    record['sub_clss_nm'] = ''
                    record['loc_cd'] = f"{record['loc_cd']} TOTAL"
                if level_name == 'Class':
                    record['grp_nm'] = original_grp_nm
                    record['dpt_nm'] = original_dpt_nm
                    record['clss_nm'] = f"{original_clss_nm} TOTAL" if original_clss_nm else "TOTAL"
                    record['sub_clss_nm'] = record.get('sub_clss_nm', '')
                elif level_name == 'Department':
                    record['grp_nm'] = original_grp_nm
                    record['dpt_nm'] = f"{original_dpt_nm} TOTAL" if original_dpt_nm else "TOTAL"
                    record['clss_nm'] = None  
                    record['sub_clss_nm'] = ''
                elif level_name == 'Group':
                    record['grp_nm'] = f"{original_grp_nm} TOTAL" if original_grp_nm else "TOTAL"
                    record['dpt_nm'] = None  
                    record['clss_nm'] = None  
                    record['sub_clss_nm'] = ''
                
                summary_totals.append(record)
        
        # Sort by store, then group, then dept, then class for proper hierarchy
        summary_totals.sort(key=lambda x: (
            x.get('loc_cd', ''),
            x.get('grp_nm', '') if x.get('grp_nm') else '',
            x.get('dpt_nm', '') if x.get('dpt_nm') else '', 
            x.get('clss_nm', '') if x.get('clss_nm') else ''
        ))
        
        return summary_totals