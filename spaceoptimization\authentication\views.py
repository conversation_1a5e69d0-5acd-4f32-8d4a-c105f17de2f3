from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from .services import AzureADService
from .models import Users, Role, UserActivityLog
from django.utils import timezone

class AzureCallbackView(APIView):
    """Exchanges authorization code for tokens and user info"""

    def post(self, request):
        # Extract code and redirect_uri from POST body
        code = request.data.get("code")
        redirect_uri = request.data.get("redirect_uri")
        # print(f"Code: {code}, Redirect URI: {redirect_uri}")
        if not code or not redirect_uri:
            return Response({
                "success": False,
                "error": "Missing authorization code or redirect URI"
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Initialize service and exchange code for tokens
            azure_service = AzureADService()
            tokens = azure_service.exchange_code_for_tokens(code, redirect_uri)
            access_token = tokens.get('access_token')
            id_token = tokens.get('id_token')
            refresh_token = tokens.get('refresh_token')

            if not access_token or not id_token:
                return Response({
                    "success": False,
                    "error": "Failed to obtain tokens"
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get user profile
            user_profile = azure_service.get_user_profile(access_token, id_token)
            
            # Check if user exists in the database
            # print(user_profile,'user email')
            try:
                user = Users.objects.get(email=user_profile.get('email'))
                # print(user, 'user found')
                
                # Check if user is active
                if not user.is_active:
                    return Response({
                        "success": False,
                        "error": "User account is not active"
                    }, status=status.HTTP_403_FORBIDDEN)
                
                # Update last login time
                user.updated_at = timezone.now()
                user.save()
                
                UserActivityLog.objects.create(
                    user=user,
                    role=user.role,
                    login_at=timezone.now()
                )
                # Add user details to response
                user_data = {
                    "id": user.id,
                    "email": user.email,
                    "name": user.first_name + " " + user.last_name,
                    "role": user.role.name if user.role else None,
                    "is_active": user.is_active
                }

                return Response({
                    "success": True,
                    "access_token": access_token,
                    "refresh_token": refresh_token,
                    "user": user_data
                }, status=status.HTTP_200_OK)

            except Users.DoesNotExist:
                return Response({
                    "success": False,
                    "error": "User is not authorized to access this application"
                }, status=status.HTTP_401_UNAUTHORIZED)

        except Exception as e:
            return Response({
                "success": False,
                "error": f"Authorization error: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserActivityView(APIView):
    def get(self, request):
        try:
            logs = UserActivityLog.objects.select_related('user', 'role').all()

            data = [
                {   
                    "id": log.id,
                    "user_id": log.user.id,
                    "first_name": log.user.first_name,
                    "last_name": log.user.last_name,
                    "email": log.user.email,
                    "role": log.role.name if log.role else None,
                    "login_at": log.login_at.strftime("%Y-%m-%d %H:%M:%S UTC"),
                }
                for log in logs
            ]

            return Response(data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                "success": False,
                "error": f"Error fetching login history: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)