from authentication.models import ApiRequestLog

class ApiTrackingMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)

        if request.path.startswith('/api/'):
            user = getattr(request, 'user', None)
            print("user is", user)
            print("request is", request.headers)
            ApiRequestLog.objects.create(
                method=request.method,
                endpoint=request.path,
                user=user if user and user.is_authenticated else None,
                email=getattr(user, 'email', None),
                first_name=getattr(user, 'first_name', None),
                last_name=getattr(user, 'last_name', None),
                status_code=response.status_code
            )
        return response
