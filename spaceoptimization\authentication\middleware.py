from authentication.models import ApiRequestLog

class ApiTrackingMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)

        if request.path.startswith('/api/'):
            user = getattr(request, 'user', None)

            if user and hasattr(user, 'is_authenticated') and user.is_authenticated:
                ApiRequestLog.objects.create(
                    endpoint=request.path,
                    user=user,
                    status_code=response.status_code
                )
        return response
